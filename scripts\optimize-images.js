const sharp = require('sharp');
const fs = require('fs');
const path = require('path');

const publicDir = path.join(__dirname, '..', 'public');

async function optimizeImages() {
  const imageExtensions = ['.png', '.jpg', '.jpeg'];
  
  async function processDirectory(dir) {
    const items = fs.readdirSync(dir);
    
    for (const item of items) {
      const fullPath = path.join(dir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory()) {
        await processDirectory(fullPath);
      } else if (imageExtensions.includes(path.extname(item).toLowerCase())) {
        await optimizeImage(fullPath);
      }
    }
  }
  
  async function optimizeImage(imagePath) {
    try {
      const ext = path.extname(imagePath).toLowerCase();
      const baseName = path.basename(imagePath, ext);
      const dir = path.dirname(imagePath);
      
      // Skip if already optimized
      if (baseName.includes('-optimized')) {
        return;
      }
      
      console.log(`Optimizing: ${imagePath}`);
      
      // Create WebP version
      const webpPath = path.join(dir, `${baseName}.webp`);
      await sharp(imagePath)
        .webp({ quality: 80 })
        .toFile(webpPath);
      
      // Create optimized original format
      const optimizedPath = path.join(dir, `${baseName}-optimized${ext}`);
      
      if (ext === '.png') {
        await sharp(imagePath)
          .png({ quality: 80, compressionLevel: 9 })
          .toFile(optimizedPath);
      } else {
        await sharp(imagePath)
          .jpeg({ quality: 80 })
          .toFile(optimizedPath);
      }
      
      console.log(`✓ Created: ${webpPath} and ${optimizedPath}`);
    } catch (error) {
      console.error(`Error optimizing ${imagePath}:`, error.message);
    }
  }
  
  await processDirectory(publicDir);
  console.log('Image optimization complete!');
}

if (require.main === module) {
  optimizeImages().catch(console.error);
}

module.exports = { optimizeImages };
