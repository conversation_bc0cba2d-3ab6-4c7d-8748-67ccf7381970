import { format, parseISO } from "date-fns";
import { formatInTimeZone } from "date-fns-tz";

export function dateFormat(date) {
  // return `${new Intl.DateTimeFormat('en-us', { day: '2-digit', month: 'short', year: 'numeric', hour: 'numeric', minute: 'numeric', second: 'numeric', timeZone: 'America/Los_Angeles', timeZoneName: 'short', hourCycle: 'h12' }).format(new Date(date))}`
  const timeZone = "America/Los_Angeles";
  const formattedDate = formatInTimeZone(
    new Date(date),
    timeZone,
    "dd-MMM-yyyy, HH:mm:ss a"
  );
  const tzAbbr = formatInTimeZone(new Date(date), timeZone, "zzz");
  return `${formattedDate} ${tzAbbr}`;
}

export const toDateObject = (date) => {
  if (typeof date !== "string" && !isNaN(Date.parse(date))) {
    return new Date(date);
  } else if (date instanceof Date && !isNaN(date)) {
    return date;
  } else if (typeof date === "string") {
    return new Date(date);
  } else {
    console.log("Unknown date format for toDateObject ", date);
    return null;
  }
};

export const formatDateFromString = (dateString) => {
  const newDate = new Date(dateString);
  return format(newDate, "MM/dd/yyyy");
};

export const formatToTimeStampFromDate = (date) => {
  return format(new Date(date), "MM/dd/yyyy, hh:mm:ss a");
};
