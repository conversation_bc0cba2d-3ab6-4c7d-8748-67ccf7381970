import dynamic from 'next/dynamic';

// Dynamic imports for heavy widgets
export const DynamicPerformanceReview = dynamic(
  () => import('./PerformanceReview/PerformanceReview').then(mod => ({ default: mod.PerformanceReview })),
  {
    loading: () => <div className="widget-loading">Loading Performance Review...</div>,
    ssr: false,
  }
);

export const DynamicMyPerformance = dynamic(
  () => import('./MyPerformance/MyPerformance').then(mod => ({ default: mod.MyPerformance })),
  {
    loading: () => <div className="widget-loading">Loading My Performance...</div>,
    ssr: false,
  }
);

export const DynamicPoliciesProcedures = dynamic(
  () => import('./PoliciesProcedures/PoliciesProcedures').then(mod => ({ default: mod.PoliciesProcedures })),
  {
    loading: () => <div className="widget-loading">Loading Policies & Procedures...</div>,
    ssr: false,
  }
);

export const DynamicPapyrusForms = dynamic(
  () => import('./PapyrusForms/PapyrusForms').then(mod => ({ default: mod.PapyrusForms })),
  {
    loading: () => <div className="widget-loading">Loading Papyrus Forms...</div>,
    ssr: false,
  }
);

export const DynamicAiChat = dynamic(
  () => import('./AiChat/AiChat').then(mod => ({ default: mod.AiChat })),
  {
    loading: () => <div className="widget-loading">Loading AI Chat...</div>,
    ssr: false,
  }
);

export const DynamicClassSchedule = dynamic(
  () => import('./ClassScedule/ClassSchedule').then(mod => ({ default: mod.ClassSchedule })),
  {
    loading: () => <div className="widget-loading">Loading Class Schedule...</div>,
    ssr: false,
  }
);
