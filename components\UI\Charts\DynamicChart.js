import dynamic from 'next/dynamic';
import { Suspense } from 'react';

// Dynamic imports for chart components with loading states
export const DynamicDoughnutChart = dynamic(
  () => import('react-chartjs-2').then((mod) => ({ default: mod.Doughnut })),
  {
    loading: () => <div className="chart-loading">Loading chart...</div>,
    ssr: false,
  }
);

export const DynamicLineChart = dynamic(
  () => import('react-chartjs-2').then((mod) => ({ default: mod.Line })),
  {
    loading: () => <div className="chart-loading">Loading chart...</div>,
    ssr: false,
  }
);

export const DynamicBarChart = dynamic(
  () => import('react-chartjs-2').then((mod) => ({ default: mod.Bar })),
  {
    loading: () => <div className="chart-loading">Loading chart...</div>,
    ssr: false,
  }
);

// Chart.js registration wrapper
export const ChartJSRegistration = dynamic(
  () => import('./ChartJSSetup'),
  { ssr: false }
);

// Wrapper component for charts with suspense
export const ChartWrapper = ({ children, fallback = <div>Loading...</div> }) => {
  return (
    <Suspense fallback={fallback}>
      {children}
    </Suspense>
  );
};
