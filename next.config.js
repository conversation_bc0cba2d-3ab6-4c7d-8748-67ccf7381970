const withBundleAnalyzer = require("@next/bundle-analyzer")({
  enabled: process.env.ANALYZE === "true",
});

/** @type {import('next').NextConfig} */
const nextConfig = {
  // Enable SWC minification for faster builds
  swcMinify: true,

  // Optimize images
  images: {
    formats: ["image/webp", "image/avif"],
    minimumCacheTTL: 60,
  },

  // Enable experimental features for better performance
  experimental: {
    // Enable modern bundling
    esmExternals: true,

    // Server actions configuration
    serverActions: {
      allowedOrigins: [
        "https://localhost:3000",
        "https://localhost",
        "https://**************",
        "https://ec2-18-222-175-215.us-east-2.compute.amazonaws.com",
      ],
      allowedForwardedHosts: [
        "https://localhost:3000",
        "https://localhost",
        "https://**************",
        "https://ec2-18-222-175-215.us-east-2.compute.amazonaws.com",
      ],
    },
  },

  webpack: (config, { isServer, dev }) => {
    // Client-side fallbacks
    if (!isServer) {
      config.resolve.fallback = {
        fs: false,
        net: false,
        tls: false,
        dns: false,
      };
    }

    // Optimize for production builds
    if (!dev) {
      // Enable webpack caching
      config.cache = {
        type: "filesystem",
        buildDependencies: {
          config: [__filename],
        },
      };

      // Optimize chunks
      config.optimization = {
        ...config.optimization,
        splitChunks: {
          chunks: "all",
          cacheGroups: {
            vendor: {
              test: /[\\/]node_modules[\\/]/,
              name: "vendors",
              chunks: "all",
            },
            charts: {
              test: /[\\/]node_modules[\\/](chart\.js|react-chartjs-2|recharts)[\\/]/,
              name: "charts",
              chunks: "all",
            },
            primereact: {
              test: /[\\/]node_modules[\\/]primereact[\\/]/,
              name: "primereact",
              chunks: "all",
            },
          },
        },
      };
    }

    return config;
  },
};

module.exports = withBundleAnalyzer(nextConfig);
