import Image from 'next/image';
import { useState } from 'react';

export const OptimizedImage = ({ 
  src, 
  alt, 
  width, 
  height, 
  className = '',
  priority = false,
  ...props 
}) => {
  const [imgSrc, setImgSrc] = useState(src);
  const [isLoading, setIsLoading] = useState(true);

  // Try to use WebP version first
  const getOptimizedSrc = (originalSrc) => {
    if (originalSrc.includes('.png') || originalSrc.includes('.jpg') || originalSrc.includes('.jpeg')) {
      const baseName = originalSrc.replace(/\.(png|jpg|jpeg)$/i, '');
      return `${baseName}.webp`;
    }
    return originalSrc;
  };

  const handleError = () => {
    // Fallback to original image if WebP fails
    if (imgSrc !== src) {
      setImgSrc(src);
    }
  };

  const handleLoad = () => {
    setIsLoading(false);
  };

  return (
    <div className={`relative ${className}`}>
      {isLoading && (
        <div 
          className="absolute inset-0 bg-gray-200 animate-pulse rounded"
          style={{ width, height }}
        />
      )}
      <Image
        src={getOptimizedSrc(imgSrc)}
        alt={alt}
        width={width}
        height={height}
        priority={priority}
        onError={handleError}
        onLoad={handleLoad}
        className={`${isLoading ? 'opacity-0' : 'opacity-100'} transition-opacity duration-300`}
        {...props}
      />
    </div>
  );
};
