@tailwind base;
@tailwind components;
@tailwind utilities;

/* Font loading is now handled by Next.js font optimization in layout.js */

/* Loading states for dynamic components */
.chart-loading,
.widget-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 200px;
  color: #666;
  font-size: 14px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.chart-loading::before,
.widget-loading::before {
  content: '';
  width: 20px;
  height: 20px;
  border: 2px solid #e9ecef;
  border-top: 2px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 10px;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

:root {
  --primary-red: #b23245;
  --primary-red-70: #7c2330;
  --primary-red-50: #591922;
  --primary-red-20: #d53c52;
  --primary-red-10: #c3374b;
  --primary-red-5: #ba3448;
  --primary-red-3: #b73347;

  --primary-bg-darkCerulean: #024f7c;
  --primary-bg-darkCerulean-70: #4d83a3;
  --primary-bg-darkCerulean-50: #80a6bd;
  --primary-bg-darkCerulean-20: #ccdce5;
  --primary-bg-darkCerulean-10: #e5edf2;
  --primary-bg-darkCerulean-5: #f2f6f8;
  --primary-bg-darkCerulean-3: #f7f9fb;

  --secondary-bg-deepBlueSky: #00b9ff;
  --secondary-bg-deepBlueSky-70: #4cceff;
  --secondary-bg-deepBlueSky-50: #7fdcff;
  --secondary-bg-deepBlueSky-20: #ccf1ff;
  --secondary-bg-deepBlueSky-10: #e5f8ff;
  --secondary-bg-deepBlueSky-5: #f2fcff;
  --secondary-bg-deepBlueSky-3: #f7fdff;

  --secondary-bg-goldenPoppy: #fdc500;
  --secondary-bg-goldenPoppy-70: #fdd64c;
  --secondary-bg-goldenPoppy-50: #fee27f;
  --secondary-bg-goldenPoppy-20: #fff4cc;
  --secondary-bg-goldenPoppy-10: #fff9e5;
  --secondary-bg-goldenPoppy-5: #fffcf2;
  --secondary-bg-goldenPoppy-3: #fffdf7;

  --tertiary-bg-limeGreen: #07a606;
  --tertiary-bg-limeGreen-20: #cdedcd;

  --primaryColor: #004990;
  --primaryColor50: #f6dde3;
  --primaryColor30: #faedf0;
  --secondaryColor: #002868;
  --secondaryColor50: #ced5e1;
  --secondaryColor30: #e5e9ef;
}

html,
body {
  margin: 0;
  font-family: var(--font-open-sans), -apple-system, BlinkMacSystemFont,
    Segoe UI, Roboto, Oxygen, Ubuntu, Cantarell, Fira Sans, Droid Sans,
    Helvetica Neue, sans-serif;
  padding: 0;
  background-attachment: fixed;
  background-position: 50%;
  background-repeat: no-repeat;
  background-size: cover;
  height: 100%;
  color: var(--secondaryColor);
  overflow-y: hidden;
}



.p-overlaypanel {
  margin-top: 20px !important;
  border-radius: 0px;
  box-shadow: 0px 3px 20px 12px rgba(0, 0, 0, 0.08);
}

.p-button {
  border: 0 !important;
  /* background-color: #fff; */
  /* color: var(--primary-red); */
  height: 50px;
  border-radius: 0px;

  @media (max-width: 768px) {
    height: 36px !important;
    font-size: 12px;
  }
}

.p-button:focus {
  box-shadow: 0 0 0 2px #ffffff;
}

.p-button-label {
  font-weight: 500 !important;
}

.customMultiselect .p-multiselect-label {
  padding: 0px 0px;
  color: #004890;
  font-size: 16px;
  font-family: "Open Sans", sans-serif;
}

.p-dropdown .p-dropdown-label {
  padding: 0px 0px;
  color: #004890;
  font-family: "Open Sans", sans-serif;
}

.p-dropdown-panel .p-dropdown-items .p-dropdown-item {
  color: #004890;
}

.p-dropdown-panel .p-dropdown-items .p-dropdown-item.p-highlight {
  background: #f3f4f6;
}

.p-multiselect-panel .p-multiselect-items .p-multiselect-item.p-highlight {
  background: #f3f4f6;
}

.p-multiselect-panel .p-multiselect-items .p-multiselect-item {
  color: #004890;
  font-size: 16px;
  padding: 12px 12px;
  font-family: "Open Sans", sans-serif;
}

.p-multiselect-panel .p-multiselect-header {
  color: #004890;
  font-size: 16px;
  padding: 12px 12px;
}

.p-checkbox .p-checkbox-box.p-highlight {
  border-color: #004890;
  background: #004890;
}

.p-dropdown-items-wrapper {
  scrollbar-width: thin;
}

.p-multiselect:focus,
.p-multiselect:active,
.p-multiselect:focus-within {
  outline: none;
  border: none;
  box-shadow: none;
}

.p-inputtext:enabled:focus {
  box-shadow: none;
}

:focus-visible {
  outline: none;
}

.p-dropdown:not(.p-disabled).p-focus {
  box-shadow: none;
}

.p-multiselect-panel .p-multiselect-items .p-multiselect-item:focus {
  outline: 0 none;
  outline-offset: 0;
  box-shadow: none;
}

.p-multiselect-panel {
  width: 12% !important;
  min-width: 10% !important;
}

.p-dropdown-panel {
  width: 11% !important;
  min-width: 10% !important;
}

.p-dropdown-items .p-dropdown-item-label {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  max-width: 176px;
  font-family: "Open Sans", sans-serif;
}

.p-checkbox .p-checkbox-box {
  border: 1px solid #004890;
  color: #004890;
}

.p-checkbox.p-highlight .p-checkbox-box {
  border-color: #004890;
  background: #004890;
}

.p-multiselect-items-wrapper {
  scrollbar-width: thin;
}

.scrollbar-thin {
  scrollbar-width: thin;
  scrollbar-color: #55555533 #f5f5f500;
}

.custom-timeline .p-timeline-event-opposite {
  flex-basis: 0;
  padding: 0;
  flex: none !important;
}

.custom-timeline .p-timeline-event {
  width: 100%;
}

.p-overlaypanel {
  margin-top: 40px;
  border-radius: 20px;
}

.p-overlaypanel .p-overlaypanel-content {
  background-color: #fff;
  border-radius: 0px;
}

.p-paginator {
  background-color: #fff;
}

/* Calendar */

.p-datepicker {
  background-color: var(--secondaryColor50);
  border-radius: 0px;
  padding: 0;
  border: none;
  overflow: hidden;
}


.p-datepicker .p-datepicker-header .p-datepicker-title .p-datepicker-year,
.p-datepicker .p-datepicker-header .p-datepicker-title .p-datepicker-month {
  color: white;
  transition: background-color 0.2s, color 0.2s, box-shadow 0.2s;
  font-weight: 600;
  padding: 0.5rem;
}

.p-datepicker .p-datepicker-header .p-datepicker-prev,
.p-datepicker .p-datepicker-header .p-datepicker-next {
  width: 2rem;
  height: 2rem;
  color: white;
  border: 0 none;
  background: transparent;
  border-radius: 50%;
  transition: background-color 0.2s, color 0.2s, box-shadow 0.2s;
}

.p-datepicker table {
  width: 100%;
  border-collapse: collapse;
  background-color: #f7f8fa;
}



.p-datepicker table td>span {
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 10px;
  transition: box-shadow 0.2s;
  border: 1px solid transparent;
  font-size: 14px;
}

.p-datepicker table td>span {
  width: 2.5rem;
  height: 2.5rem;
  color: var(--secondaryColor);
  border-radius: 10px;
  transition: box-shadow 0.2s;
  /* border: 1px solid transparent; */
}

.p-datepicker table thead {
  padding: 0.5rem;
  background-color: var(--secondaryColor50);
  border-radius: 30px;
  color: #004890;
}

.p-datepicker span.p-highlight {
  background: var(--secondary-bg-goldenPoppy-70);
  color: #004890;
}

.p-datepicker:not(.p-disabled) table td span:not(.p-highlight):not(.p-disabled):focus {
  background-color: var(--secondary-bg-goldenPoppy-70);
  color: #004890;
}

/* Datatable Here */
.p-datatable .p-datatable-thead>tr>th {
  text-align: left;
  font-weight: 700;
  padding: 0.5rem;
  border: solid #dee2e6 !important;
  border-width: 0 0 1px !important;
  color: #002138 !important;
  background: #ccd2d7 !important;
  transition: box-shadow 0.2s !important;
}

.p-datatable .p-datatable-tbody>tr {
  transition: box-shadow 0.2s;
  font-size: 14px !important;
  font-weight: 600;
  color: #002138 !important;
  height: 48px;
}

tr.p-row-odd {
  background: #e5f8ff !important;
}

.p-paginator-bottom.p-paginator.p-component {
  padding: 0;
}

.p-datatable .p-datatable-tbody>tr>td {
  text-align: left;
  border: 1px solid #e5e7eb;
  border-width: 0 0 1px 0;
  padding: 0rem 0.5rem;
}

/* Breadcrumb here */
.p-breadcrumb {
  background: transparent;
  border: 1px solid transparent;
  color: #fff;
  border-radius: 6px;
  padding: 1rem 0rem;
}

.p-breadcrumb .p-breadcrumb-list li:last-child .p-menuitem-text {
  color: #fff;
}

.p-breadcrumb .p-breadcrumb-list li.p-menuitem-separator {
  margin: 0 0.5rem 0 0.5rem;
  color: #fff;
}

span.p-menuitem-icon.pi.pi-home {
  color: #fff;
}

.p-accordion-content {
  color: #004890;
}

.p-accordion-content li {
  text-decoration: underline;
  color: blue;
  margin-bottom: 10px;
}

/* .recharts-default-legend {
  width: 84%;
  margin: 0 0 0 40px !important;
} */

.recharts-legend-item {
  margin-left: 22px !important;
}

.recharts-surface {
  height: 90% !important;
}

.recharts-legend-item-text {
  color: #004890 !important;
  margin-left: 8px;
  font-size: 14px;
}

.p-multiselect.p-multiselect-chip .p-multiselect-token {
  padding: 0;
  margin-right: 0;
  background: none;
  color: #004890;
  border-radius: 0;
}


/* Blue Theme */
/* 
.navbarContainer {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  background-color: #004890;
  padding: 0 200px;
  height: 100px;
  position: relative;
  z-index: 1000;
}

.footer {
  background-color: #004890;
  color: white;
  text-align: center;
  padding: 10px 0;
  width: 100%;
  height: 30px;
  position: relative;
  z-index: 10;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.widgetHeader {
  font-size: 20px;
  font-weight: 700;
  color: #004890;
  border-bottom: none;
  margin-top: 0;
  padding-bottom: 0;

  @media (max-width: 768px) {
    font-size: 16px;
  }
}

.secondary-button-color {
  background-color: #004890 !important;
  color: white !important;
  width: 300px;
}

.p-datepicker .p-datepicker-header {
  padding: 0.5rem;
  background: #004890;
  font-weight: 600;
  margin: 0;
  border-bottom: 1px solid #e5e7eb;
  border-radius: 0px;
}

.p-datepicker table td.p-datepicker-today>span.p-highlight {
  color: #fff;
  background: #004890;
}

.widgetContainer {
  width: 30%;
  display: flex;
  flex-direction: column;
  gap: 10px;
  padding: 10px 20px;
  border-radius: 0px;
  background-color: #fff;
  border: 0.5px solid #00489078;
}

.headerContainer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 10px;
  border-bottom: 1px solid #004890;
  margin-top: -10px;
}

.headerInputSearch {
  border-color: #00489099;
}

.headerSearchIcon {
  color: #004890;
}

.loginButton {
  background-color: #004890;
} */


/* .p-datepicker table td.p-datepicker-today>span {
  background: #004890;
  color: #fff;
  border-color: transparent;
} */






/* Red Theme */
.navbarContainer {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  background-color: #c22042;
  padding: 0 200px;
  height: 100px;
  position: relative;
  z-index: 1000;
}

.footer {
  background-color: #c22042;
  color: white;
  text-align: center;
  padding: 10px 0;
  width: 100%;
  height: 30px;
  position: relative;
  z-index: 10;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.widgetHeader {
  font-size: 20px;
  font-weight: 700;
  color: #c12042;
  border-bottom: none;
  margin-top: 0;
  padding-bottom: 0;

  @media (max-width: 768px) {
    font-size: 16px;
  }
}

.secondary-button-color {
  background-color: #c12042 !important;
  color: white !important;
  width: 300px;
}

.p-datepicker .p-datepicker-header {
  padding: 0.5rem;
  background: #c12042;
  font-weight: 600;
  margin: 0;
  border-bottom: 1px solid #e5e7eb;
  border-radius: 0px;
}

.p-datepicker table td.p-datepicker-today>span.p-highlight {
  color: #fff;
  background: #c12042;
}

.widgetContainer {
  width: 30%;
  display: flex;
  flex-direction: column;
  gap: 10px;
  padding: 10px 20px;
  border-radius: 0px;
  background-color: #fff;
  border: 0.5px solid #c2204263;
}

.headerContainer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 10px;
  border-bottom: 1px solid #c12042;
  margin-top: -10px;
}

.headerInputSearch {
  border-color: #c120426e;
}

.headerSearchIcon {
  color: #c12042;
}

.loginButton {
  background-color: #c22042;
}

.p-datepicker table td.p-datepicker-today>span {
  background: #c22042;
  color: #fff;
  border-color: transparent;
}